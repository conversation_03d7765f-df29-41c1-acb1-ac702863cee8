{"fileName": "ReduxLib-2025.0.1.json", "name": "ReduxLib", "version": "2025.0.1", "frcYear": "2025", "uuid": "151ecca8-670b-4026-8160-cdd2679ef2bd", "mavenUrls": ["https://maven.reduxrobotics.com/"], "jsonUrl": "https://frcsdk.reduxrobotics.com/ReduxLib_2025.json", "javaDependencies": [{"groupId": "com.reduxrobotics.frc", "artifactId": "ReduxLib-java", "version": "2025.0.1"}], "jniDependencies": [{"groupId": "com.reduxrobotics.frc", "artifactId": "ReduxLib-driver", "version": "2025.0.1", "isJar": false, "skipInvalidPlatforms": true, "validPlatforms": ["linux<PERSON>ena", "linuxx86-64", "linuxarm32", "linuxarm64", "osxuniversal", "windowsx86-64"]}], "cppDependencies": [{"groupId": "com.reduxrobotics.frc", "artifactId": "ReduxLib-cpp", "version": "2025.0.1", "libName": "ReduxLib", "headerClassifier": "headers", "sourcesClassifier": "sources", "sharedLibrary": true, "skipInvalidPlatforms": true, "binaryPlatforms": ["linux<PERSON>ena", "linuxx86-64", "linuxarm32", "linuxarm64", "osxuniversal", "windowsx86-64"]}, {"groupId": "com.reduxrobotics.frc", "artifactId": "ReduxLib-driver", "version": "2025.0.1", "libName": "ReduxCore", "headerClassifier": "headers", "sharedLibrary": true, "skipInvalidPlatforms": true, "binaryPlatforms": ["linux<PERSON>ena", "linuxx86-64", "linuxarm32", "linuxarm64", "osxuniversal", "windowsx86-64"]}]}