{"fileName": "URCL.json", "name": "URCL", "version": "2025.0.1", "frcYear": "2025", "uuid": "84246d17-a797-4d1e-bd9f-c59cd8d2477c", "mavenUrls": ["https://frcmaven.wpi.edu/artifactory/littletonrobotics-mvn-release/"], "jsonUrl": "https://raw.githubusercontent.com/Mechanical-Advantage/URCL/main/URCL.json", "javaDependencies": [{"groupId": "org.littletonrobotics.urcl", "artifactId": "URCL-java", "version": "2025.0.1"}], "jniDependencies": [{"groupId": "org.littletonrobotics.urcl", "artifactId": "URCL-driver", "version": "2025.0.1", "skipInvalidPlatforms": true, "isJar": false, "validPlatforms": ["windowsx86-64", "linuxx86-64", "linux<PERSON>ena", "osxuniversal"]}], "cppDependencies": [{"groupId": "org.littletonrobotics.urcl", "artifactId": "URCL-cpp", "version": "2025.0.1", "libName": "URCL", "headerClassifier": "headers", "sharedLibrary": false, "skipInvalidPlatforms": true, "binaryPlatforms": ["windowsx86-64", "linuxx86-64", "linux<PERSON>ena", "osxuniversal"]}, {"groupId": "org.littletonrobotics.urcl", "artifactId": "URCL-driver", "version": "2025.0.1", "libName": "URCLDriver", "headerClassifier": "headers", "sharedLibrary": false, "skipInvalidPlatforms": true, "binaryPlatforms": ["windowsx86-64", "linuxx86-64", "linux<PERSON>ena", "osxuniversal"]}]}