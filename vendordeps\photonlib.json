{"fileName": "photonlib.json", "name": "photonlib", "version": "v2025.3.1", "uuid": "515fe07e-bfc6-11fa-b3de-0242ac130004", "frcYear": "2025", "mavenUrls": ["https://maven.photonvision.org/repository/internal", "https://maven.photonvision.org/repository/snapshots"], "jsonUrl": "https://maven.photonvision.org/repository/internal/org/photonvision/photonlib-json/1.0/photonlib-json-1.0.json", "jniDependencies": [{"groupId": "org.photonvision", "artifactId": "photontargeting-cpp", "version": "v2025.3.1", "skipInvalidPlatforms": true, "isJar": false, "validPlatforms": ["windowsx86-64", "linux<PERSON>ena", "linuxx86-64", "osxuniversal"]}], "cppDependencies": [{"groupId": "org.photonvision", "artifactId": "photonlib-cpp", "version": "v2025.3.1", "libName": "photonlib", "headerClassifier": "headers", "sharedLibrary": true, "skipInvalidPlatforms": true, "binaryPlatforms": ["windowsx86-64", "linux<PERSON>ena", "linuxx86-64", "osxuniversal"]}, {"groupId": "org.photonvision", "artifactId": "photontargeting-cpp", "version": "v2025.3.1", "libName": "photontargeting", "headerClassifier": "headers", "sharedLibrary": true, "skipInvalidPlatforms": true, "binaryPlatforms": ["windowsx86-64", "linux<PERSON>ena", "linuxx86-64", "osxuniversal"]}], "javaDependencies": [{"groupId": "org.photonvision", "artifactId": "photonlib-java", "version": "v2025.3.1"}, {"groupId": "org.photonvision", "artifactId": "photontargeting-java", "version": "v2025.3.1"}]}