{"fileName": "AdvantageKit.json", "name": "AdvantageKit", "version": "4.1.2", "uuid": "d820cc26-74e3-11ec-90d6-0242ac120003", "frcYear": "2025", "mavenUrls": ["https://frcmaven.wpi.edu/artifactory/littletonrobotics-mvn-release/"], "jsonUrl": "https://github.com/Mechanical-Advantage/AdvantageKit/releases/latest/download/AdvantageKit.json", "javaDependencies": [{"groupId": "org.littletonrobotics.akit", "artifactId": "akit-java", "version": "4.1.2"}], "jniDependencies": [{"groupId": "org.littletonrobotics.akit", "artifactId": "akit-wpilibio", "version": "4.1.2", "skipInvalidPlatforms": false, "isJar": false, "validPlatforms": ["linux<PERSON>ena", "linuxx86-64", "linuxarm64", "osxuniversal", "windowsx86-64"]}], "cppDependencies": []}